import {openURL} from 'expo-linking';
import {useCallback, useEffect, useMemo} from 'react';
import {firebaseApi} from '@api';
import type {ContextMenuActionInternal} from '@base-components';
import {CONTENT_CODES, SYSTEM_ICONS} from '@constants';
import {
  useRecreateSummaryReport,
  useSendWelcomeEmailMutation,
  useSignedReportUrl,
} from '@data-hooks';
import {
  useCurrentTimeZoneWithDefault,
  useDateEveryMinute,
  useInvalidateChallengeParticipant,
  useOnPressActionSheet,
  useUpdatedRef,
} from '@hooks';
import {useLinkTo} from '@navigation';
import {
  type AppUser,
  type BaseAppUser,
  type CardAction,
  type ChallengeGroupDocument,
  ChallengeGroupingType,
  type ChallengePostGroups,
  type ChallengePostTeams,
  type ChallengeStage,
  type ChallengeTeam,
  type GroupLevelMetadata,
  type Initializer,
  isChallengePostGroups,
  type IsoDate,
  type Meal,
  type Organization,
  StatusCodes,
  type SummaryReport,
  type UUIDString,
  type Workout,
} from '@types';
import {
  actionSheetLog,
  alertConfirm,
  getGroupName,
  getIsoDateAsDateAtCurrentTime,
  getMealAsDuplicate,
  getWorkoutTransformed,
  isEmptyArray,
} from '@utils';
import {useAppUserSafe, useIsCoach} from './authContext';
import {useDeleteAccount} from './authContextLogin';
import {useRecalculateGroup} from './challengeOperationsContext';
import {useSelectedChallengeStage} from './createChallengeContext';
import {
  useDeleteAppUserById,
  useDeleteChallengeById,
  useDeleteChallengePostById,
  useDeleteMealById,
  useDeleteOrganizationById,
  useDeleteOrganizationPostById,
  useDeleteSummaryReportById,
  useDeleteWorkoutById,
} from './firestore';
import {usePersistedSelectedOrganization} from './persistedSelectedOrganizationIdContext';
import {useAddSnack} from './snacks';

export const useViewChallengeActionSheet = (
  challengeId: UUIDString,
  canLeaveChallenge: boolean,
) => {
  const {id: userId} = useAppUserSafe();
  const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deleteChallenge} = useDeleteChallengeById();
  const invalidate = useInvalidateChallengeParticipant(challengeId, userId);

  return useOnPressActionSheet({
    options: canLeaveChallenge
      ? {
          [CONTENT_CODES().CHALLENGE.ACTION_SHEET.LEAVE_CHALLENGE]: async () => {
            await alertConfirm(
              'Leave Challenge',
              'Are you sure you want to leave this challenge? This action cannot be undone.',
              async () => {
                const {status} = await firebaseApi.leaveChallenge(challengeId, userId);
                if (status === StatusCodes.OK_200) {
                  to.pop();
                  actionSheetLog(`Left challenge ${challengeId} successfully`);
                  addSnack('Left the challenge successfully ✅');
                  void invalidate();
                } else {
                  addSnack('There was an issue leaving the challenge');
                }
              },
              () => {
                actionSheetLog(`Cancelled leaving challenge ${challengeId}`);
              },
            );
          },
          [CONTENT_CODES().CHALLENGE.ACTION_SHEET.CANCEL]: () => {
            LOGGER.debug('[ActionSheet] Cancelled ViewChallengeScreen action sheet');
            return Promise.resolve();
          },
        }
      : {
          [CONTENT_CODES().CHALLENGE.ACTION_SHEET.EDIT]: () => {
            to.editChallengeScreen({challengeId, userSearchTerm: ''});
            return Promise.resolve();
          },
          [CONTENT_CODES().CHALLENGE.ACTION_SHEET.DELETE]: async () => {
            const isSuccess = await deleteChallenge(challengeId);
            if (!isSuccess) return;
            to.pop();
            addSnack('Challenge deleted successfully ✅');
          },
          [CONTENT_CODES().CHALLENGE.ACTION_SHEET.CANCEL]: () => {
            LOGGER.debug('[ActionSheet] Cancelled ViewChallengeScreen action sheet');
            return Promise.resolve();
          },
        },
    destructiveButtonIndex: canLeaveChallenge ? 0 : 1,
    cancelButtonIndex: canLeaveChallenge ? 1 : 2,
  });
};

export const useProfileActionSheet = (appUser: AppUser) => {
  const to = useLinkTo();
  const appUserRef = useUpdatedRef(appUser);

  const deleteAccount = useDeleteAccount();
  return useOnPressActionSheet({
    options: {
      [CONTENT_CODES().VIEW_PROFILE.EDIT_PROFILE_BUTTON]: () => {
        LOGGER.debug(
          '[ActionSheet] Editing profile from ProfilePage action sheet',
          appUserRef.current.email,
        );
        to.editProfile({userId: appUserRef.current.id});
        return Promise.resolve();
      },
      [CONTENT_CODES().SETTINGS.ACCOUNT_ACTIONS.DELETE_ACCOUNT]: async () => {
        LOGGER.debug('[ActionSheet] Deleting user account', appUserRef.current.email);
        await deleteAccount();
      },
      [CONTENT_CODES().VIEW_PROFILE.ACTION_SHEET_CANCEL]: () => {
        LOGGER.debug('[ActionSheet] Cancelled ProfilePage action sheet');
        return Promise.resolve();
      },
    },
    destructiveButtonIndex: 1,
    cancelButtonIndex: 2,
  });
};

export const useWorkoutEditActionSheet = (workoutId: UUIDString) => {
  const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deleteWorkout} = useDeleteWorkoutById();

  return useOnPressActionSheet({
    options: {
      // [CONTENT_CODES().WORKOUT.EDIT_WORKOUT_BUTTON]: () => {
      //   LOGGER.debug('[ActionSheet] EditWorkout action sheet', ref.current);
      //   to.editWorkoutScreen({id});
      // },
      [CONTENT_CODES().WORKOUT.ACTION_SHEET.DELETE]: async () => {
        LOGGER.debug(`[ActionSheet] Deleting workout ${workoutId}`);
        const isSuccess = await deleteWorkout(workoutId);
        if (!isSuccess) return;
        to.pop();
        addSnack('Workout deleted successfully ✅');
      },
      [CONTENT_CODES().WORKOUT.ACTION_SHEET.CANCEL]: () => {
        LOGGER.debug('[ActionSheet] Cancelled EditWorkout action sheet');
        return Promise.resolve();
      },
    },
    cancelButtonIndex: 1,
    destructiveButtonIndex: 0,
  });
};

export const useViewOrganizationActionSheet = (id: UUIDString) => {
  const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deleteOrganization} = useDeleteOrganizationById();

  return useOnPressActionSheet({
    options: {
      [CONTENT_CODES().ORGANIZATION.ACTION_SHEET.DELETE]: async () => {
        LOGGER.debug('[ActionSheet] Deleting organization', id);
        const isSuccess = await deleteOrganization(id);
        if (!isSuccess) return;
        to.pop();
        addSnack('Organization deleted successfully ✅');
      },
      [CONTENT_CODES().ORGANIZATION.ACTION_SHEET.CANCEL]: () => {
        LOGGER.debug('[ActionSheet] Cancelled ViewOrganization action sheet');
        return Promise.resolve();
      },
    },
    cancelButtonIndex: 1,
    destructiveButtonIndex: 0,
  });
};

// export const useOrganizationPostActionSheet = (organizationId: string, postId: string) => {
//   const to = useLinkTo();
//   const deletePost = useDeleteOrganizationPostById(organizationId);

//   return useOnPressActionSheet({
//     options: {
//       [CONTENT_CODES().FEED.POSTS.ACTION_SHEET.EDIT]: () => {
//         LOGGER.debug('[ActionSheet] Editing organization post', postId);
//         to.editOrganizationPost({organizationId, id: postId});
//         return Promise.resolve();
//       },
//       [CONTENT_CODES().FEED.POSTS.ACTION_SHEET.DELETE]: async () => {
//         LOGGER.debug('[ActionSheet] Deleting organization post', postId);
//         await deletePost(postId);
//       },
//       [CONTENT_CODES().FEED.POSTS.ACTION_SHEET.CANCEL]: () => {
//         LOGGER.debug('[ActionSheet] Cancelled organization post action sheet');
//         return Promise.resolve();
//       },
//     },
//     cancelButtonIndex: 2,
//     destructiveButtonIndex: 1,
//   });
// };

export const useAppUserContextActions = (appUser: BaseAppUser | undefined) => {
  const {mutateAsync: deleteAppUser} = useDeleteAppUserById();
  const {mutateAsync: sendWelcomeEmail} = useSendWelcomeEmailMutation();
  const addSnack = useAddSnack();
  const to = useLinkTo();

  return useMemo(() => {
    if (!appUser) return [];
    return [
      {
        title: CONTENT_CODES().EDIT_USER.SEND_WELCOME_EMAIL,
        onPress: async () => {
          LOGGER.debug('[ContextAction] Sending welcome email', appUser.id);
          await alertConfirm(
            'Send Welcome Email',
            `Do you want to send the welcome email to "${appUser.email}"? Press OK to confirm.`,
            async () => {
              await sendWelcomeEmail(appUser.id);
              addSnack(`Sent welcome email: ${appUser.email}`);
            },
            () => {
              LOGGER.debug(`[ContextAction] Cancelled sending welcome email: ${appUser.email}`);
            },
          );
        },
        icons: SYSTEM_ICONS.sendEmail,
      },
      {
        title: CONTENT_CODES().EDIT_USER.DELETE_USER_LABEL,
        onPress: async () => {
          LOGGER.debug('[ContextAction] Deleting app user', appUser.id);
          await deleteAppUser(appUser.id);
          LOGGER.debug('[ContextAction] Successfully deleted', appUser.id);
          to.pop();
          addSnack('User deleted successfully ✅');
        },
        icons: SYSTEM_ICONS.delete,
        destructive: true,
      },
    ];
  }, [appUser, deleteAppUser, sendWelcomeEmail, addSnack, to]);
};

export const useClientContextActions = (appUser: BaseAppUser | undefined) => {
  const to = useLinkTo();
  const appUserActions = useAppUserContextActions(appUser);

  return useMemo(() => {
    if (!appUser) return [];
    return [
      {
        title: 'Edit Client',
        onPress: () => {
          to.editUserScreen({userId: appUser.id, organizationSearch: ''});
        },
        icons: SYSTEM_ICONS.editProfile,
      },
      ...appUserActions,
    ];
  }, [appUser, appUserActions, to]);
};

export const useCreateChallengeContextActions = () => {
  const to = useLinkTo();

  return useMemo(
    () => [
      {
        title: CONTENT_CODES().CHALLENGE.ACTION_SHEET.CREATE_INDIVIDUAL,
        onPress: () =>
          to.createChallengeScreen({
            userSearchTerm: '',
            groupingType: ChallengeGroupingType.INDIVIDUAL,
          }),
        icons: SYSTEM_ICONS.individualChallenge,
      },
      {
        title: CONTENT_CODES().CHALLENGE.ACTION_SHEET.CREATE_TEAMS,
        onPress: () =>
          to.createChallengeScreen({
            userSearchTerm: '',
            groupingType: ChallengeGroupingType.TEAMS,
          }),
        icons: SYSTEM_ICONS.teamsChallenge,
      },
      {
        title: CONTENT_CODES().CHALLENGE.ACTION_SHEET.CREATE_GROUPS,
        onPress: () =>
          to.createChallengeScreen({
            userSearchTerm: '',
            groupingType: ChallengeGroupingType.GROUPS,
          }),
        icons: SYSTEM_ICONS.groupsChallenge,
      },
    ],
    [to],
  );
};

export const useOrganizationSelectContextActions = (organizations: Organization[] | undefined) => {
  const to = useLinkTo();
  const [{organizationId}, setPersistedOrganizationId] = usePersistedSelectedOrganization();

  // Load first organization as selected org if exactly one organization for the user
  useEffect(() => {
    if (organizationId || isEmptyArray(organizations) || organizations.length > 1) return;
    const firstOrgId = organizations.at(0)?.id;
    if (!firstOrgId) return;
    setPersistedOrganizationId({organizationId: firstOrgId});
  }, [organizationId, organizations, setPersistedOrganizationId]);

  return useMemo(
    () => [
      ...(organizations ?? []).map(org => ({
        title: org.name,
        onPress: () => {
          setPersistedOrganizationId({organizationId: org.id});
          to.organizationFeed({organizationId: org.id});
        },
        icons: SYSTEM_ICONS.changeProfile,
        // eslint-disable-next-line @typescript-eslint/naming-convention -- library
        selected: org.id === organizationId,
      })),
      {
        title: 'No organization selected',
        onPress: () => {
          setPersistedOrganizationId({organizationId: undefined});
          to.organizationFeed({organizationId: undefined});
        },
        icons: SYSTEM_ICONS.none,
      },
    ],
    [organizations, organizationId, setPersistedOrganizationId, to],
  );
};

export const useOrganizationPostCardContextActions = (orgId: UUIDString, orgPostId: UUIDString) => {
  const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deletePost} = useDeleteOrganizationPostById(orgId);

  return useMemo(
    () => [
      {
        title: CONTENT_CODES().FEED.POSTS.ACTION_SHEET.EDIT,
        onPress: () => {
          LOGGER.debug('[ActionSheet] Editing organization post', orgPostId);
          to.editOrganizationPost({
            organizationId: orgId,
            postId: orgPostId,
          });
        },
        icons: SYSTEM_ICONS.edit,
      },
      {
        title: CONTENT_CODES().FEED.POSTS.ACTION_SHEET.DELETE,
        onPress: async () => {
          LOGGER.debug('[ActionSheet] Deleting organization post', orgPostId);
          const isSuccess = await deletePost(orgPostId);
          if (!isSuccess) return;
          addSnack('Post deleted successfully ✅');
        },
        icons: SYSTEM_ICONS.delete,
        destructive: true,
      },
    ],
    [addSnack, deletePost, orgId, orgPostId, to],
  );
};

export const useChallengePostCardContextActions = (
  challengeId: UUIDString,
  post: ChallengePostTeams | ChallengePostGroups,
) => {
  const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deletePost} = useDeleteChallengePostById(
    challengeId,
    isChallengePostGroups(post) ? post.groupId : undefined,
  );

  return useMemo(
    () => [
      {
        title: CONTENT_CODES().FEED.POSTS.ACTION_SHEET.EDIT,
        onPress: () => {
          LOGGER.debug('[ActionSheet] Editing challenge post', post.id);
          to.editChallengePost({
            challengeId,
            challengePostId: post.id,
            groupId: isChallengePostGroups(post) ? post.groupId : undefined,
          });
        },
        icons: SYSTEM_ICONS.edit,
      },
      {
        title: CONTENT_CODES().FEED.POSTS.ACTION_SHEET.DELETE,
        onPress: async () => {
          LOGGER.debug('[ActionSheet] Deleting challenge post', post.id);
          const isSuccess = await deletePost(post.id);
          if (!isSuccess) return;
          addSnack('Post deleted successfully ✅');
        },
        icons: SYSTEM_ICONS.delete,
        destructive: true,
      },
    ],
    [addSnack, challengeId, deletePost, post, to],
  );
};

export const useChallengeTrainerMessageContextActions = (options: {
  challengeId: UUIDString;
  groupId?: UUIDString | undefined | null;
  teamId?: UUIDString | undefined | null;
}): ContextMenuActionInternal[] => {
  const to = useLinkTo();

  return [
    {
      title: CONTENT_CODES().CHALLENGE.ACTION_SHEET.TRAINER_SEND_MESSAGE,
      onPress: () => {
        to.trainerMessageScreen(options);
      },
      icons: SYSTEM_ICONS.message,
    },
  ];
};

export const useChallengeTrainerMessageSelectGroupsActions = (
  allGroups: {id: UUIDString; name: string}[] | undefined,
  onIdsChange: (ids: Initializer<(UUIDString | null)[]>) => void,
  type: 'teams' | 'groups',
) => {
  const allIds = allGroups?.map(t => t.id) ?? [];

  const noTeamSelectedOption = {
    title: 'No team selected',
    onPress: () => {
      onIdsChange([]);
    },
    icons: SYSTEM_ICONS.none,
  };
  const allTeamsSelectedOption = {
    title: 'All Teams',
    onPress: () => {
      onIdsChange(allIds);
    },
    icons: SYSTEM_ICONS.broadcastMessage,
  };

  return [
    noTeamSelectedOption,
    allTeamsSelectedOption,
    ...(allGroups ?? []).map(g => ({
      title: g.name,
      onPress: () => {
        onIdsChange([g.id]);
      },
      icons: type === 'teams' ? SYSTEM_ICONS.teamsChallenge : SYSTEM_ICONS.groupsChallenge,
    })),
  ];
};

export const useChallengeTeamsSelectContextActions = (
  teams: ChallengeTeam[],
  setTeamId: (id: UUIDString | undefined) => void,
) => {
  const isTrainer = useIsCoach();
  return useMemo(() => {
    if (!isTrainer) return [];
    const noTeamSelectedOption = {
      title: 'No team selected',
      onPress: () => {
        setTeamId(undefined);
      },
      icons: SYSTEM_ICONS.none,
    };
    return [
      noTeamSelectedOption,
      ...teams.map(team => ({
        title: `${team.isPublicTeam ? '🅿 ' : ''}${team.name}`,
        onPress: () => {
          setTeamId(team.id);
        },
        icons: SYSTEM_ICONS.teamsChallenge,
      })),
    ];
  }, [isTrainer, setTeamId, teams]);
};

export const useChallengeGroupsSelectContextActions = (
  groups: ChallengeGroupDocument[] | undefined,
  setGroup?: (group: ChallengeGroupDocument | undefined) => void,
) => {
  const isTrainer = useIsCoach();
  return useMemo(() => {
    if (!setGroup) return;
    if (isEmptyArray(groups)) return [];
    const noTeamSelectedOption = {
      title: 'No group selected',
      onPress: () => {
        setGroup(undefined);
      },
      icons: SYSTEM_ICONS.none,
    };
    return [
      ...(isTrainer ? [noTeamSelectedOption] : []),
      ...groups.map(group => ({
        title: `${group.isPublicGroup ? '🅿 ' : ''}${getGroupName(group)}`,
        onPress: () => {
          setGroup(group);
        },
        icons: SYSTEM_ICONS.groupsChallenge,
      })),
    ];
  }, [setGroup, groups, isTrainer]);
};

export const useChallengeLevelSelectContextActions = (
  levels: GroupLevelMetadata[] | undefined,
  setLevel?: (level: number) => void,
) =>
  useMemo(() => {
    if (!setLevel || !levels) return;
    if (levels.length === 1) return [];
    return levels.map(level => ({
      title: level.label,
      onPress: () => {
        setLevel(level.level);
      },
      icons: SYSTEM_ICONS.challengeLevels,
    }));
  }, [setLevel, levels]);

export const useChallengeSelectedStageContextActions = (
  challengeId: UUIDString,
  stages: ChallengeStage[] | undefined,
) => {
  const {setSelectedStage} = useSelectedChallengeStage(challengeId);

  return useMemo(() => {
    if (isEmptyArray(stages)) return [];
    // const noTeamSelectedOption = {
    //   title: 'No stage selected',
    //   onPress: () => {
    //     setSelectedStage(undefined);
    //   },
    //   icons: SYSTEM_ICONS.none,
    // };
    return stages.map(stage => ({
      title: stage.name,
      onPress: () => {
        setSelectedStage(stage);
      },
      icons: SYSTEM_ICONS.stageSelector,
    }));
  }, [setSelectedStage, stages]);
};

export const useChallengeGroupActions = (
  challengeId: UUIDString,
  group: ChallengeGroupDocument,
) => {
  const isTrainer = useIsCoach();
  const {mutateAsync: recalculateGroup} = useRecalculateGroup(challengeId);
  return useMemo(
    () =>
      isTrainer
        ? [
            {
              title: CONTENT_CODES().CHALLENGE.ACTION_SHEET.RECALCULATE_GROUP_DATA,
              onPress: () => recalculateGroup(group.id),
              icons: SYSTEM_ICONS.refresh,
            },
          ]
        : undefined,
    [group.id, isTrainer, recalculateGroup],
  );
};

export const useSummaryReportContextActions = (summaryReportId: UUIDString) => {
  // const to = useLinkTo();
  const addSnack = useAddSnack();
  const {mutateAsync: deleteSummaryReport} = useDeleteSummaryReportById();

  return useMemo(
    () => [
      {
        title: CONTENT_CODES().SUMMARY_REPORTS.ACTION_SHEET.DELETE,
        onPress: async () => {
          LOGGER.debug('[ActionSheet] Deleting summary report', summaryReportId);
          const isSuccess = await deleteSummaryReport(summaryReportId);
          if (!isSuccess) return;
          addSnack('Summary report deleted successfully ✅');
        },
        icons: SYSTEM_ICONS.delete,
        destructive: true,
      },
    ],
    [addSnack, deleteSummaryReport, summaryReportId],
  );
};

export const useSummaryReportUserContextActions = (summaryReport: SummaryReport) => {
  const addSnack = useAddSnack();
  const {mutateAsync} = useRecreateSummaryReport(summaryReport.id);
  const {mutateAsync: getSignedReportUrl} = useSignedReportUrl(summaryReport.id);

  return useCallback(
    (userId: UUIDString) => {
      const isComplete = summaryReport.userIdsCompleted?.includes(userId);
      if (isComplete) {
        return {
          userContextActions: [
            {
              title: CONTENT_CODES().SUMMARY_REPORTS.ACTION_SHEET.OPEN,
              onPress: async () => {
                addSnack('Fetching link for report...');
                const url = await getSignedReportUrl(userId);
                if (!url) return;

                LOGGER.debug(`Opening report url: ${url.slice(0, 100)}`);
                await openURL(url);
              },
              icons: SYSTEM_ICONS.download,
            },
            {
              title: CONTENT_CODES().SUMMARY_REPORTS.ACTION_SHEET.RECREATE_REPORT,
              onPress: async () => {
                addSnack('Recreating report...');
                await mutateAsync(userId);
                addSnack('Re-generated report and sent to user');
              },
              icons: SYSTEM_ICONS.refresh,
            },
          ],
          status: 'complete',
          cardAction: {
            label: 'Sent',
            icon: 'check-outline',
          } satisfies CardAction,
        };
      }
      const isFailed = summaryReport.userIdsFailed?.includes(userId);
      return {
        userContextActions: [],
        status: isFailed ? 'failed ' : 'incomplete',
        cardAction: isFailed
          ? ({label: 'Not Sent', icon: 'close-outline'} satisfies CardAction)
          : ({label: 'Processing', isLoading: true} satisfies CardAction),
      };
    },
    [
      addSnack,
      getSignedReportUrl,
      mutateAsync,
      summaryReport.userIdsCompleted,
      summaryReport.userIdsFailed,
    ],
  );
};

export const useCreateWorkoutContextActions = (
  date: IsoDate | undefined,
  workout: Workout | undefined,
  initialUserIdInLists?: UUIDString,
) => {
  const to = useLinkTo();
  const today = useDateEveryMinute([workout?.startedDateTime]);
  const initialWorkout = useMemo(
    () => (workout ? getWorkoutTransformed(workout, today, initialUserIdInLists) : undefined),
    [workout, today, initialUserIdInLists],
  );
  const timeZone = useCurrentTimeZoneWithDefault();
  const initialStartDate = date && getIsoDateAsDateAtCurrentTime(date, timeZone);

  const contextActions = useMemo(
    () => [
      {
        title: CONTENT_CODES().WORKOUT.ACTION_SHEET.CREATE_WORKOUT_SELF,
        onPress: () => {
          if (!initialStartDate) return;
          const params = {
            userSearchTerm: '',
            initialStartDate,
            ...(initialWorkout && {initialWorkout}),
          };
          LOGGER.debug('[ActionSheet] Navigating to user create workout', params);
          to.createWorkoutScreen(params);
        },
        icons: SYSTEM_ICONS.createForSelf,
      },
      {
        title: CONTENT_CODES().WORKOUT.ACTION_SHEET.CREATE_WORKOUT_TRAINER,
        onPress: () => {
          if (!initialStartDate) return;
          const params = {
            userSearchTerm: '',
            isTrainerCreate: true,
            initialStartDate,
            ...(initialWorkout && {initialWorkout}),
          } as const;
          LOGGER.debug('[ActionSheet] Navigating to trainer create workout', params);
          to.createWorkoutScreen(params);
        },
        icons: SYSTEM_ICONS.createForClients,
      },
    ],
    [initialStartDate, initialWorkout, to],
  );
  const onPress = useCallback(() => {
    if (!initialStartDate) return;
    const params = {
      userSearchTerm: '',
      initialStartDate,
      ...(initialWorkout && {initialWorkout}),
    };
    LOGGER.debug('[ActionSheet] Navigating to user create workout, onPress', params);
    to.createWorkoutScreen(params);
  }, [initialStartDate, initialWorkout, to]);

  return {
    contextActions,
    onPress,
  };
};

export const useCreateMealContextActions = (
  date: IsoDate | undefined,
  meal: Meal | undefined,
  userId: UUIDString,
) => {
  const to = useLinkTo();
  const today = useDateEveryMinute([meal?.dateTime]);
  const initialMeal = useMemo(
    () => (meal ? getMealAsDuplicate(meal, today, userId) : undefined),
    [userId, meal, today],
  );
  const timeZone = useCurrentTimeZoneWithDefault();

  const contextActions = useMemo(
    () => [
      {
        title: CONTENT_CODES().MEAL.ACTION_SHEET.CREATE_MEAL_SELF,
        onPress: () => {
          const initialStartDate = date && getIsoDateAsDateAtCurrentTime(date, timeZone);
          if (!initialStartDate) return;
          LOGGER.debug('[ActionSheet] Navigating to user create meal');
          to.createMealScreen({
            ...(initialMeal && {initialMeal}),
            userSearchTerm: '',
            initialStartDate,
          });
        },
        icons: SYSTEM_ICONS.createForSelf,
      },
      {
        title: CONTENT_CODES().MEAL.ACTION_SHEET.CREATE_MEAL_TRAINER,
        onPress: () => {
          const initialStartDate = date && getIsoDateAsDateAtCurrentTime(date, timeZone);
          if (!initialStartDate) return;
          LOGGER.debug('[ActionSheet] Navigating to trainer create meal');
          to.createMealScreen({
            ...(initialMeal && {initialMeal}),
            userSearchTerm: '',
            isTrainerCreate: true,
            initialStartDate,
          });
        },
        icons: SYSTEM_ICONS.createForClients,
      },
    ],
    [initialMeal, date, timeZone, to],
  );

  const onPress = useCallback(() => {
    const initialStartDate = date && getIsoDateAsDateAtCurrentTime(date, timeZone);
    if (!initialStartDate) return;
    to.createMealScreen({...(initialMeal && {initialMeal}), userSearchTerm: '', initialStartDate});
  }, [date, timeZone, to, initialMeal]);

  return {
    contextActions,
    onPress,
  };
};

export const useEditMealActionSheet = (id: UUIDString) => {
  const to = useLinkTo();
  const addSnack = useAddSnack();

  const {mutateAsync: deleteMeal} = useDeleteMealById();
  return useOnPressActionSheet({
    options: {
      [CONTENT_CODES().MEAL.ACTION_SHEET.DELETE]: async () => {
        LOGGER.debug('[ActionSheet] Deleting meal', id);
        const isSuccess = await deleteMeal(id);
        if (!isSuccess) return;
        to.pop();
        addSnack('Meal deleted successfully ✅');
      },
      [CONTENT_CODES().MEAL.ACTION_SHEET.CANCEL]: () => {
        LOGGER.debug('[ActionSheet] Cancelled EditMeal action sheet');
        return Promise.resolve();
      },
    },
    cancelButtonIndex: 1,
    destructiveButtonIndex: 0,
  });
};
